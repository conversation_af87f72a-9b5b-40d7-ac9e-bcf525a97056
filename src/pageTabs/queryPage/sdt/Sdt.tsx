import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react'
import { TreeProps } from 'rc-tree/lib/Tree'
import * as _ from 'lodash'
import classnames from 'classnames'
import {
  useSelector,
  useDispatch,
  useRequest,
} from 'src/hook'
import { ISearchSdtItem, getUserConfig } from 'src/api'
import { cloneDeep, debounce } from 'lodash'
import {
  Tree,
  Row,
  Select,
  Spin,
  Tooltip,
  Input,
  message,
  Popover,
  Menu,
  Dropdown,
} from 'antd'
import { BdNavigation, Iconfont, SdtPermissionSwitch, ThemeSwitch } from 'src/components'
import {
  CreateConnectionModal,
  CreateDatabaseModal,
} from 'src/features/wizards'
import { getSpecialConnectionTypePermissions, actionIconNodeTypes } from 'src/constants'
import { DropdownMenu } from './DropdownMenu'
import {
  setTreeData,
  setSelectedNode,
  setMulSelectedNodes,
  setRightClickedNode,
  setExpandedKeys,
  setLoadedKeys,
  refreshOnRoot,
  fetchConnections,
  viewElementInEditor,
  showViewInEditor,
  NodeTypesSupportEditorView,
  viewRedisKey,
  getTreeNodeChildren,
  changeGroupByType,
  clearRefreshDebounce,
  setIsRefreshingNode,
  addToOnLoadQueue,
  clearOnLoadQueue,
  resetSdtToInitialState
} from './sdtSlice'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import styles from './index.module.scss'
import { addPane, setNewSdtSelectedKeysVal } from '../queryTabs/queryTabsSlice'
import {
  setTargetTableList,
  setTargetTableMessage,
  setNoAccessRightClick,
} from 'src/store/extraSlice/textImportSlice'
import {
  NodeEntity,
  sortConnection_api,
  moveConnectionToSdtGroup,
  addRequisitionToCart,
  getSearchSdtList
} from 'src/api'
import { openFlowForm } from 'src/pageTabs/flowPages/flowFormsSlice'
import {
  AddSdtGroupModal,
  AddSdtNodeExportModal,
  ModalAddSchema,
  MoveToSdtGroupModal,
  RenameSdtNodeModal,
  AddSubSdtGroupModal,
  UpdateConnAliasModal,
  BatchCreateConnectionModal,
  DumpExportModal,
  DumpImportModal,
  SQLImportModal,
  CreateFunctionModal,
  CreateProcedureModal,
  AddGroupConnectionModal
} from './modals'
import { findTreeParentNode, findTreeRootNode, getSdtThinPermissionIconType } from 'src/util'
import type { DataNode } from 'antd/lib/tree'
import type { DataSourceType, ResponseNodeEntity, TreeNode } from 'src/types'
import moduleService from 'src/service/moduleService'
import {
  matchKeyword,
  getExpandNodeNeedPathKeys,
} from './const.sdt'
import { setAddToCartFlag } from '../queryPageSlice'
import { useLocation } from 'react-router-dom'
import { getParentKeysAboutContainSearchValue } from 'src/util';
import { ConnectionFailWarnImg } from 'src/components/ConnectionFailWarnImg'
import { setHdrVisible } from 'src/store/extraSlice/hdrSlice';
import { useTranslation } from 'react-i18next'
import sdtDisplayTypeImg from 'src/assets/img/icon_display_type.png'
import classNames from 'classnames'

interface RenderNode extends ResponseNodeEntity, DataNode {
  valid?: boolean
  enable?: boolean
}

const { DirectoryTree } = Tree

// 树节点titleRender的tooltip内容
const getExtraNodeTooltipContent = (t: any, node: any) => {
  const { nodeOptions, nodeType, connectionType, displayName, title, remark } = node
  const { comments, isNullable, dataLength, dataType, remark:  procedureRemark} = nodeOptions || {}
  if ((nodeType === 'column' && connectionType !== 'MongoDB')) {
    return <div className={classnames(styles.columnTooltipContent, styles.tooltipContent)}>
      <div>{t('sdo_type')}：{dataType}</div>
      <div>{t('sdo_length')}：{dataLength}</div>
      <div>{t('sdo_allow_null')}：{isNullable ? 'true' : 'false'}</div>
      <div>{t('sdo_comment')}：{comments}</div>
    </div>
  }
  else if (nodeType === "connection") {
    return <div className={styles.tooltipContent}>
      <div>{t('sdo_connection_name')}：{title}</div>
      <div>{t('sdo_remark')}：{remark}</div>
    </div>
  } else if (nodeType === 'procedure') {
    return (
      <div>
        {displayName}<span className={styles.nodeRemark}>{procedureRemark}</span>
      </div>
    )
  }
  else {
    return displayName
  }
}

export const noTablePermission = (node: any) => {
  const { nodeType, havingPermission = false } = node
  return actionIconNodeTypes?.includes(nodeType) && !havingPermission
}

export const Sdt = React.memo(() => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const location = useLocation<any>()
  const BatchCreateConnectionModalVisible = useSelector(
    (state) => state.modal['BatchCreateConnectionModal']?.visible,
  )
  const addGroupConnectionModalVisible = useSelector(
    (state) => state.modal['AddGroupConnectionModal']?.visible,
  )
  //同构复制 禁止右键等可操作项
  const { hdrVisible } = useSelector(state => state.hdrGuide);
  const { roles = [], permissionMenus = [] } = useSelector((state) => state.login.userInfo);


  // 只有高级用户和DBA角色 包含自定义角色 可以创建任务
  const staticDesensMenuAuth = permissionMenus.find(pMenu => pMenu?.menuType === "DATA_TRANSFORM")?.items?.find(i => i?.permissionType === 'DATA_TRANSFORM')?.accessMode === 'WRITE_AUTHED';

  const canCreateTask = roles?.map(i => i?.type)?.includes('SENIOR_ROLE') ||
    roles?.map(i => i?.type)?.includes('DBA_ROLE') ||
    (roles?.map(i => i?.type)?.some(i => i.startsWith("CUSTOM_SYSTEM")) && staticDesensMenuAuth)

  const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds)
  const searchInputRef = useRef<Input>(null)
  const { devModelConnectionIds = [] } = useSelector((state) => state.editor)
  const { dataSourceMap } = useSelector((state) => state.dataSource)
  const newSdtSelectedKeysVal = useSelector((state) => state.queryTabs.newSdtSelectedKeysVal)
  // const { dictSearchIsOn } = useDictSearch() // 后端已删除对应接口
  const [keyword, setKeyword] = useState('')
  const [searchActive, setSearchActive] = useState(true)
  const [hoverNodeKey, setHoverNodeKey] = useState<string | number>('')
  const [hoverNodePathWithType, setHoverNodePathWithType] = useState('')
  const [dictSearchValue, setDictSearchValue] = useState<any>(null)
  const [dictOptions, setDictOptions] = useState<any>([])
  const [treeHeight, setTreeHeight] = useState<number>(300)
  const [loadMoreLoading, setLoadMoreLoading] = useState<any>({})
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(false)

  useEffect(() => {
    queryTreeHeight()
    window.addEventListener('resize', queryTreeHeight)
    return () => {
      window.removeEventListener('resize', queryTreeHeight)
       // 页面卸载时清除 debounce
       clearRefreshDebounce();
    }
  }, [])

  const queryTreeHeight = () => {
    const clientHeight = document.documentElement.clientHeight
    const treeHeight = clientHeight > 436 ? clientHeight - 200 : 326
    setTreeHeight(treeHeight)
  }

  useEffect(() => {
    dispatch(setSelectedNode({}))
    dispatch(fetchConnections())
    return () => {
      dispatch(setTreeData([]))
    }
  }, [dispatch])

  useEffect(() => {
    if (searchActive) {
      searchInputRef.current?.focus()
    }
  }, [searchActive])

  const {
    userInfo: { userId },
  } = useSelector((state) => state.login)
  const {
    treeData,
    selectedNode,
    mulSelectedNodes,
    expandedKeys,
    loadedKeys,
    treeLoading,
    treeNodeChildrenMap,
    resourceMap,
    groupByType,
    isRefreshingNode,
    onLoadQueue
  } = useSelector((state) => state.sdt)
  const { addToCartFlag } = useSelector((state) => state.queryPage)
  const { connectionId, connectionType, nodeName, nodeType, nodePath, nodePathWithType, maskedPathMap } = selectedNode || {}
  const sdtNodeExportParams = {
    connectionId,
    connectionType,
    nodeName,
    nodeType,
    nodePath,
    nodePathWithType,
    maskedPathMap,
  }

  useEffect(() => {
    if (selectedNode || mulSelectedNodes.length > 1) {
      dispatch(setNewSdtSelectedKeysVal(''))
    }
  }, [dispatch, selectedNode, mulSelectedNodes])

  const onLoadData = useCallback(
    async (treeNode: DataNode) => {
      const node = treeNode as RenderNode
      const { supportPaging, connectionType, nodeType, nodePath } = node

      let params = { ...node }
      
      // 添加节点到加载队列
      if (nodePath) {
        dispatch(addToOnLoadQueue(nodePath))
      }
      
      // 子级支持分页,默认请求第一页
      const { sdtPageSize } = await getUserConfig();
      if (supportPaging) {
        params = {
          ...params,
          pageSize: sdtPageSize ?? 2000,
          pageNo: 1,
          notMemorize: true, // 每次请求数据不全,不缓存值
          startPageNum: 1,
        }
      }
      // redis加分页，默认请求第一页
      if (connectionType === 'Redis' && nodeType === 'connection') {
        params = {
          ...params,
          pageSize: 10,
          pageNo: 1,
          notMemorize: true, // 每次请求数据不全,不缓存值
          startPageNum: 1,
        }
      }

      await dispatch(getTreeNodeChildren(params))
    },
    [dispatch],
  )

  const { run: addRun } = useRequest(addRequisitionToCart, {
    manual: true,
    onSuccess(data) {
      let num = addToCartFlag + 1
      message.success(t('sdo_join_application_suc'))
      dispatch(setAddToCartFlag(num))
    }
  })

  /* 无权连接 唤起流程表单 ,数据操作权限对应连接访问提权*/
  /* todo: 整合到后端提取逻辑中 */
  // 目前有连接权限和操作权限，后期应该在后端处理好
  const openDataSourceFlow = async (node: NodeEntity) => {
    const { connectionId, nodeName, connectionType, applyPermissionType } = node
    const permissionNodes = [
      {
        connectionId,
        connectionType,
        nodeName,
        nodePath: `/root/${connectionId}`,
        nodeType: 'connection',
      },
    ] as any

    // 能看到连接的情况下应该是有连接访问权限了，这个时候缺的是数据操作权限
    if (applyPermissionType === 'connectionAccess') {
      dispatch(
        openFlowForm({
          type: 'connectionAccess',
          fields: {
            elements: permissionNodes,
          },
        }),
      )
    } else {
      dispatch(
        openFlowForm({
          type: 'dataManipulation',
          fields: {
            elements: permissionNodes,
          },
        }),
      )
    }
  }

  const handleNodeSelect = (
    _keys: any,
    e: { selected: boolean; selectedNodes: any; node: any; event: any },
  ) => {
    const { selected, selectedNodes, node } = e

    // ctrl/cmd+鼠标左键多选
    if (selectedNodes.length > 1) {
      dispatch(setMulSelectedNodes(selectedNodes))
      return
    }
    //table无权限
    const perm = noTablePermission(node)
    if (perm) {
      return
    }

    if (selected) {
      dispatch(setSelectedNode(node))
      dispatch(setMulSelectedNodes([]))
    }
  }

  interface SdtHeaderIconProps {
    iconType: string
    onClick?: () => void
    title?: string
    style?: React.CSSProperties
    disabled?: boolean
  }

  const SdtHeaderIcon = (props: SdtHeaderIconProps) => {
    const { title, onClick, iconType, style, disabled } = props
    
    const handleClick = () => {
      if (!disabled && onClick) {
        onClick()
      }
    }
    
    return (
      <Tooltip placement="bottomRight" title={title || ''}>
        <Iconfont
          className={styles.sdtToolbarIcon}
          type={iconType}
          onClick={handleClick}
          style={{
            ...style,
            cursor: disabled ? 'not-allowed' : 'pointer',
            opacity: disabled ? 0.5 : 1
          }}
        ></Iconfont>
      </Tooltip>
    )
  }

  const renderTitle = (i: any, searchValue: string) => {
    let iconType = `icon-${i?.nodeType === 'connection' ? i?.dataSourceType : i?.nodeType}`
    //暂时没有具体到列的无权限图标 目前图标不会置灰
    if (actionIconNodeTypes?.includes(i?.nodeType) && !i?.havingPermission) {
      iconType = `icon-${i?.nodeType}_noPerm`
    }

    const parts = i?.nodeShowName?.split(new RegExp(`(${searchValue})`, 'gi'));

    return <div>
      <Iconfont type={iconType} style={{ marginRight: 4 }} />
       <span style={!i?.havingPermission ? { color: '#ccc' } : {}}>
        {parts?.map((part: string, index: number) =>
          part?.toLowerCase() === searchValue?.toLowerCase() ?
            <span key={index} style={{ color: '#3357ff' }}>{part}</span>
            : part
        )}
      </span>
    </div>
  }
  //数据字典远程搜索前20条
  const { loading: dictLoading, run: onSearchSdtNodes } = useRequest(getSearchSdtList, {
    manual: true,
    formatResult: (data) => {
      return data?.map((i: ISearchSdtItem) => ({
        label: i?.nodeShowName,
        value: i?.nodePath,
        key: i?.nodePath,
        ...i
      }))
    }
  })

  const toggleSearchActive = () => {
    setSearchActive(!searchActive)
    setDictSearchValue(null);
    setKeyword('')
  }

  const onSelectFilterOption = async (v: string, option: any) => {
    setDictSearchValue(v);
    delete option?.children;
    const searchName: string = _.last(option?.nodeShowName?.split('.')) || ''
    const { nodePath, dataSourceType } = option;
    const allExpandKeys = getExpandNodeNeedPathKeys(option?.nodePath, true);
 
    //nodePath 所有父级
    let shouldExpandKeys = allExpandKeys.slice(0, allExpandKeys.length - 1);

    //再次搜索 tree已经生成 不需要再次请求接口
    if (treeNodeChildrenMap[nodePath]) {
      setKeyword(searchName);
      // 按数据库类型展示时，需要将数据库类型作为第一个展开的节点
      if (groupByType) {
        shouldExpandKeys = [dataSourceType].concat(shouldExpandKeys);
      }
      dispatch(setExpandedKeys([...new Set(shouldExpandKeys)]))
     
      await dispatch(setSelectedNode(option));
      return
    }
    // 父级 nodePath总长度
    const parentNodePathWithTypes = getExpandNodeNeedPathKeys(nodePathWithType);

    //以下层级 nodePathWithTypes 和上个节点保持一致
    const level1Names = [
      t('common.dictSearch.nodeName.table'),
      t('common.dictSearch.nodeName.foreignTable'),
      t('common.dictSearch.nodeName.group'),
      t('common.dictSearch.nodeName.collection'),
      t('common.dictSearch.nodeName.materializedView'),
      t('common.dictSearch.nodeName.keyGroup'),
      t('common.dictSearch.nodeName.fileGroup'),
      t('common.dictSearch.nodeName.functionGroup'),
      t('common.dictSearch.nodeName.procedureGroup'),
      t('common.dictSearch.nodeName.taskGroup'),
      t('common.dictSearch.nodeName.synonym'),
      t('common.dictSearch.nodeName.sequenceGroup'),
      t('common.dictSearch.nodeName.triggerGroup'),
      t('common.dictSearch.nodeName.databaseConnection'),
      t('common.dictSearch.nodeName.package'),
      t('common.dictSearch.nodeName.packageBody'),
      t('common.dictSearch.nodeName.taskGroup'),
      t('common.dictSearch.nodeName.dictionaryGroup'),
      t('common.dictSearch.nodeName.gridFsBucket'),
      t('common.dictSearch.nodeName.userDefinedFunction'),
      'flexTableGroup'
    ]
    const level2Names = [
      t('common.dictSearch.nodeName.columnGroup'),
      t('common.dictSearch.nodeName.indexGroup'),
      t('common.dictSearch.nodeName.constraintGroup'),
      t('common.dictSearch.nodeName.foreignKeyGroup'),
      t('common.dictSearch.nodeName.triggerGroup'),
      t('common.dictSearch.nodeName.partitionGroup')
    ];
    let asyncActions: any = [];
    let nodePathWithTypeArr = parentNodePathWithTypes;

    //@ts-ignore
    shouldExpandKeys?.map((key: string, index: number) => {
      //是否是表...结尾
      let endType = key.substr(key.lastIndexOf('/') + 1);
      //如果是组 则nodePathWithType = null;
      if (/^\/root\/(g-\d+)(\/g-\d+)*$/.test(key)) {
        //@ts-ignore
        nodePathWithTypeArr.splice(index, 0, null);
      } else if (level1Names.includes(endType) || level2Names.includes(endType)) {
        const preNType = nodePathWithTypeArr[index - 1]; //这里可能不对
        nodePathWithTypeArr.splice(index, 0, preNType);
      }
    })

    shouldExpandKeys.map((key, index) => asyncActions.push({ nodePath: key, nodePathWithType: nodePathWithTypeArr[index] }))
   
    for (const node of asyncActions) {
      try {

        //防止重新请求 导致其他查询过数据被清空
        if (!treeNodeChildrenMap[node.nodePath]) {
          await dispatch(getTreeNodeChildren(node))
        };

      } catch (error) {
        console.log('异步调用出错了', error);
        break;
      }
    }
    // 按数据库类型展示时，需要将数据库类型作为第一个展开的节点
    if(groupByType){
      shouldExpandKeys = [dataSourceType].concat(shouldExpandKeys);
    }
    await dispatch(setExpandedKeys([]))
    await dispatch(setLoadedKeys([]));
    // await dispatch(setSelectedNode(null));

    await dispatch(setExpandedKeys([...new Set(shouldExpandKeys)]));
    await dispatch(setSelectedNode(option));
    setKeyword(searchName)
  }

  const onDictSearchSdtNodes = async (value: { query: string }) => {

    const res = await onSearchSdtNodes(value);

    setDictOptions(res)
  }

  const displayMenus = (
    <Menu>
      <Menu.Item
        className={classNames({ [styles.activeMenuItem]: !groupByType })}
        disabled={isRefreshingNode}
        onClick={() => dispatch(changeGroupByType(false))}
      >
        {t("sdo_menu_display_type_normal")}
      </Menu.Item>
      <Menu.Item
        className={classNames({ [styles.activeMenuItem]: groupByType })}
        disabled={isRefreshingNode}
        onClick={() => dispatch(changeGroupByType(true))}
      >
        {t("sdo_menu_display_type_group_by_type")}
      </Menu.Item>
    </Menu>
  );

  const SdtHeader = (
    <Row className={styles.sdtHeader} justify="space-between" align="middle">
      <ThemeSwitch />
      <Row justify="end" style={{ alignItems: 'center' }}>
        {/* SDT树的展示类型 */}
        <Dropdown
          overlay={displayMenus}
          trigger={['click']}
          className={styles.sdtDisplayType}
        >
          <Tooltip title={t('sdo_tip_sdt_display_type')} placement="left">
            <img src={sdtDisplayTypeImg} alt={t('sdo_tip_sdt_display_type')} />
          </Tooltip>
        </Dropdown>
        <SdtPermissionSwitch
          disabled={isRefreshingNode}
          onChange={() => dispatch(refreshOnRoot())}
        />
        {/* 按数据库类型展示时，不显示添加分组 */}
        {
          !groupByType &&
          <SdtHeaderIcon
            title={t('sdo_add_group')}
            onClick={() => dispatch(showModal("AddSdtGroup"))}
            iconType="icon-folderplus-fill"
            disabled={isRefreshingNode}
          />
        }
        <SdtHeaderIcon
          title={t('sdo_pack_nodes')}
          onClick={() => dispatch(setExpandedKeys([]))}
          iconType="icon-collapse"
          disabled={isRefreshingNode}
        />
        <SdtHeaderIcon
          title={t('sdo_refresh')}
          onClick={() => {
            dispatch(refreshOnRoot())
            setKeyword('')
            setDictSearchValue(null)
          }}
          iconType="icon-sync-alt"
          disabled={isRefreshingNode}
        />
        {
          canCreateTask &&
          <SdtHeaderIcon
            title={t('sdo_homogeneous_replication')}
            onClick={() => dispatch(setHdrVisible(true))}
            iconType="icon-dataDuplication"
          />
        }
      </Row>
    </Row>
  );

  const titleRender = (node: RenderNode) => {
    const {
      title,
      nodeType,
      connectionType,
      key,
      alias,
      parentGroupId,
      childCount,
      nodeOptions,
      applyPermissionType,
      valid,
      enable,
      connectionId,
      permissionList = {},
      nodePathWithType,
      userEnable = false,
      maskedPathMap = {}, //敏感资源
      isLoadMore  // 查看更多
    } = node

    const { dataType, remark, dataTypeWithLength } = nodeOptions || {}
    const isShowNodeActionIcons = actionIconNodeTypes?.includes(nodeType) && !isLoadMore

    const getExtraNodeInfo = () => {
      /* 0 列 显示类型，hover 显示其他 */
      if (nodeType === 'column' && (dataType || dataTypeWithLength)) {
        return <span className={styles.extraNodeInfo}>[{dataTypeWithLength || dataType}]</span>
      }
      if (childCount) {
        return <span className={styles.extraNodeInfo}>({childCount})</span>
      }
      return null
    }

    const getNodeIcon = () => {
      if (isLoadMore) {
        return null
      }
      switch (nodeType) {
        case "datasource":
          return (
            <Iconfont
              className="mr8"
              type={'icon-connection-' + key}
            />
          )
        case 'connectionGroup':
          return (
            <Iconfont
              style={{ color: '#3f84e9' }}
              type="icon-folder-fill"
              className="mr8"
            ></Iconfont>
          )
        case 'connection':
          return (
            <Iconfont
              // 不同的sdt展示类型，连接的图标不一样
              type={groupByType ? `icon-${connectionType}` : `icon-connection-${connectionType}`}
              className="mr8"
              style={expandedKeys.includes(key) ? {} : { opacity: 0.5 }}
            ></Iconfont>
          )
        default:
          let defaultIconType = `icon-${nodeType}`
          if (noTablePermission(node)) {
            defaultIconType = `icon-${nodeType}_noPerm`
          } else if (valid === false) {
            defaultIconType = `icon-${nodeType}_disabled`
          } else if (enable === false && nodeType === 'trigger') {
            defaultIconType = `icon-${nodeType}_off`
          }
          return <Iconfont
            type={defaultIconType}
            className="mr8"
          ></Iconfont>
      }
    }
    const nodeIcon = getNodeIcon()
    const displayName = (isLoadMore ? `${t('sdo_load_more')}...` : alias) ?? title
    /* 1 未展开 opacity 0.8 */
    const notExpanded = !expandedKeys.includes(key)
    /* 2 未授权连接 */
    const connectionUnavailable =
      nodeType === 'connection' && treeNodeChildrenMap[key]?.length === 0
    const isAccessDenied =
      moduleService.isModuleExist('/flow') &&
      (parentGroupId === -1 || connectionUnavailable)

    const resourceFlag = resourceMap[connectionId]

    const handleClickNode = async () => {
      // 表层级分页加载数据
      if (isLoadMore) {
        setLoadMoreLoading((loading: any) => ({ ...loading, [key]: true }))
        try {
          // pageSize在渲染loadmore时已处理
          await dispatch(getTreeNodeChildren(node))
        } catch (e) {
          console.error('分页加载更多error', e)
        } finally {
          setLoadMoreLoading((loading: any) => ({ ...loading, [key]: false }))
        }
      }
      if (resourceFlag) return
      if (!isAccessDenied) return
      openDataSourceFlow(node)
    }

    const dataSourceTypeLevel = (type: DataSourceType) => {
      let level: number = dataSourceMap?.[type]?.dataSourceLevel || 3;
      if (type === 'SQLServer') {
        level = 3;
      }
      return level
    }
    //表下的触发组不能提权
    const hidePermissionIcon = (hoverNodePathWithType?.split('/').length - 1 > dataSourceTypeLevel(connectionType)) && (nodeType === 'triggerGroup' || nodeType === 'trigger')

    const applyAccess = nodeType === 'connection' && applyPermissionType === 'connectionAccess'

    return (
      <Spin spinning={!!loadMoreLoading[key]}>
        <div
          className={styles.sdtTitleWrap}
          title=""
          onClick={handleClickNode}
          id={`${key}_key`}
          onMouseEnter={() => {
            if (isShowNodeActionIcons) {
              setHoverNodePathWithType(nodePathWithType);
              setHoverNodeKey(key)
            }
          }}
          onMouseLeave={() => {
            if (isShowNodeActionIcons) {
              setHoverNodeKey('');
              setHoverNodePathWithType('');
            }
          }}
        >
          {
            !resourceFlag && isAccessDenied && !applyAccess &&
            <span className={styles.applyAccess}>{t('sdo_request_operation_permission')}</span>
          }
          {
            applyAccess &&
            <span className={styles.applyAccess}>{t('sdo_request_connection_permission')}</span>
          }
          <div
            className={classnames(
              { [styles.notExpanded]: notExpanded },
              { [styles.sdtTitle]: !resourceFlag && isAccessDenied },
              { [styles.loadMoreTitle]: isLoadMore },
              styles.treeTxtWrap
            )}
          >
            {nodeIcon}
            {/* 表层级图标重叠问题 */}
            <div className={classnames(styles.autoTableWithIcon, {[styles.hoverAutoTableWithIcon]:  hoverNodeKey === key && isShowNodeActionIcons && !hidePermissionIcon &&nodeType === 'table'}) }>
              <Tooltip
                title={getExtraNodeTooltipContent(t, { ...node, displayName })}
                placement="top"
                overlayClassName={styles.sdtTitleTooltip}
              >
                <span className={styles.treeTxt}>{displayName}
                  {getExtraNodeInfo()}
                  {remark && <span className={styles.nodeRemark}>{remark}</span>}
                </span>
              </Tooltip>
              {
                devModelConnectionIds?.includes(connectionId) &&
                nodeType === "connection" &&
                <Tooltip title={t('sdo_developer_mode')}>
                  <Iconfont
                    type="icon-developer"
                    className={styles.permissionLimitIcon}
                  />
                </Tooltip>
              }
              {maskedPathMap && Object.keys(maskedPathMap)?.length > 0 && nodeType === 'table' &&
                <Popover
                  content={t('sdo_sensitive_resources')}>
                  <Iconfont
                    type={Object.keys(maskedPathMap).every(key => !!maskedPathMap[key]) ? "icon-lock_sensitive" : "icon-lock"}
                    className={styles.permissionLimitIcon}
                    onClick={() => {
                      if (Object.keys(maskedPathMap).every(key => !!maskedPathMap[key]) || hdrVisible) return
                      //无权限的脱敏资源
                      const noPermissionSensitiveResourceElements = Object.keys(maskedPathMap).filter(key => !maskedPathMap[key]).map(key => ({
                        label: '',
                        value: key
                      }));

                      dispatch(
                        openFlowForm({
                          type: 'desensitizedResource',
                          fields: {
                            //@ts-ignore
                            elements: noPermissionSensitiveResourceElements,
                            //@ts-ignore
                            connectionId,
                            connectionType,
                            nodeType,
                          },
                        })
                      );
                    }}
                  ></Iconfont>
                </Popover>
              }
              {
                nodeType === "connection" && allFailedCountConnectionIds?.includes(Number(connectionId)) &&
                <ConnectionFailWarnImg />
              }
              </div>
              {
                hoverNodeKey === key && isShowNodeActionIcons && !hidePermissionIcon && userEnable &&
                <span className={styles.tablePermTip}>
                  {
                    getSpecialConnectionTypePermissions(connectionType)[nodeType]?.map((type: string) => {
                      let permissionTypeStatus = permissionList[type]
                      const permissionType = getSdtThinPermissionIconType(type);

                      let iconType = permissionTypeStatus ? `icon-type-${permissionType}` : `icon-type-${permissionType}_disabled`
                      return (
                        <Tooltip title={type} key={type}>
                          <Iconfont
                            type={iconType}
                            className={styles.permissionLimitIcon}
                            onClick={() => {
                              //同构复制开启禁止操作
                              if (hdrVisible) return;

                              if (!permissionTypeStatus) {
                                addRun({
                                  flowType: 'THIN',
                                  nodeType,
                                  nodePath: node?.nodePath,
                                  nodePathWithType,
                                  connectionId,
                                  dataSourceType: connectionType,
                                  nodeName: node?.nodeName,
                                  operation: type
                                })
                              }
                            }}
                          />
                        </Tooltip>
                      )
                    })
                  }
                </span>
              }
          </div>
        </div>
      </Spin>
    )
  }

  /* 搜索交给接口, 实现全局节点搜索 */
  const filterNodesNotMatch = useCallback(
    (nodes: TreeNode[]): TreeNode[] =>
      nodes.filter((node) => {
        //@ts-ignore
        const name = _.last(selectedNode?.nodeShowName?.split('.')) || ''
        const sLength = selectedNode?.nodePath?.split('/').length || 0;
        const nLength = node?.nodePath?.split('/').length || 0;


        //后端搜索 筛选信息只有一条， 前端筛选模糊匹配
        const keywordHit = searchActive ?
          (matchKeyword(node.nodeName, keyword) ||
            matchKeyword(node.alias, keyword))
          //@ts-ignore
          : sLength === nLength && matchKeyword(node.nodeName, name)  //确保位置准确 不然可能出现多个层级

        if (!keywordHit && node.children) {
          node.children = filterNodesNotMatch(node.children)
        }
        return keywordHit || node.children?.length
      }),
    [keyword, searchActive],
  )

  const filteredTreeData = useMemo(() => {
    return keyword ? filterNodesNotMatch(cloneDeep(treeData)) : treeData
  }, [filterNodesNotMatch, treeData])

  useEffect(() => {
    if (searchActive && filteredTreeData) {
      if (keyword) {
        debounce(()=>{
          let parentKeys: any = getParentKeysAboutContainSearchValue(filteredTreeData, keyword) || []
          setAutoExpandParent(false)
          const keys: any = [...new Set(parentKeys.concat(expandedKeys ?? []))]
          dispatch(setExpandedKeys(keys))
        },500)()
      }
      else {
        setAutoExpandParent(false)
      }
    } else {
      setAutoExpandParent(false)
    }
  }, [keyword, searchActive])

 // 监听 onLoadQueue 的变化，实现基于超时机制的刷新完成判断
  useEffect(() => {
    let normalTimeoutId: NodeJS.Timeout | null = null
    let safetyTimeoutId: NodeJS.Timeout | null = null
    
    if (isRefreshingNode) {
      // 正常机制：队列为空时3秒后完成刷新
      if (onLoadQueue.length === 0) {
        normalTimeoutId = setTimeout(() => {
          dispatch(setIsRefreshingNode(false))
        }, 3000)
      }
      
      // 安全机制：无论如何，15秒后强制完成刷新并重置 SDT 状态
      safetyTimeoutId = setTimeout(() => {
        // 重置 SDT 到初始状态，清除所有相关数据
        dispatch(resetSdtToInitialState())
        console.log('触发安全机制：SDT 已重置到初始状态')
      }, 15000)
    }

    return () => {
      if (normalTimeoutId) {
        clearTimeout(normalTimeoutId)
      }
      if (safetyTimeoutId) {
        clearTimeout(safetyTimeoutId)
      }
    }
  }, [onLoadQueue, isRefreshingNode, dispatch])

  const handleDoubleClick = (e: any, node: any) => {
    //同构复制开启禁止右键功能
    //当表收起时候 selectedNode依然有值
    if (!selectedNode || (selectedNode?.nodeName !== node?.nodeName) || hdrVisible) return

    const { nodeType, nodePath, parentGroupId } = selectedNode
    if (parentGroupId === -1) return
    if (expandedKeys.some((key) => key === nodePath)) return
    if (NodeTypesSupportEditorView?.includes(nodeType)) {
      if (
        nodeType === 'sequence' ||
        nodeType === 'package' ||
        nodeType === 'type' ||
        nodeType === 'packageBody' ||
        nodeType === 'trigger'
      )
        dispatch(showViewInEditor(selectedNode))
      else dispatch(viewElementInEditor(selectedNode))
    }
    if (nodeType === 'redisKey') {
      dispatch(viewRedisKey(selectedNode))
    }
    // Redis 以外的连接，双击直接打开查询
    if (nodeType === 'connection' && connectionType !== 'Redis') {
      dispatch(
        addPane({
          tabName: nodeName,
          connectionType,
          connectionId,
        }),
      )
    }
  }

  const isConnecttionNode = ({ nodeType }: any) => nodeType === 'connection'
  const isConnectionGroupNode = ({ nodeType }: any) =>
    nodeType === 'connectionGroup'
  const isSameGroupMember = (nodeA: any, nodeB: any) =>
    nodeA.parentGroupId === nodeB.parentGroupId
  const isAccessDenyConnectionNode = ({
    parentGroupId,
  }: {
    parentGroupId: number
  }) => parentGroupId === -1
  const isAccessDenyGroupNode = ({ groupId }: { groupId: number }) =>
    groupId === -1
  const isAccessDenyNode = (node: any) =>
    isAccessDenyConnectionNode(node) || isAccessDenyGroupNode(node)
  // 拖拽排序
  // todo:组不支持拖拽
  const onDrop: TreeProps['onDrop'] = async (info) => {
    try {
      // 如果正在刷新节点，则不允许拖拽操作
      if (isRefreshingNode) {
        return
      }
      
      const { node, dragNode, dropToGap, dropPosition } = info as any

      // 0 无权限组不允许拖拽或排序
      // 1 drag 节点只允许为数据源类型节点
      if (
        !isConnecttionNode(dragNode) ||
        isAccessDenyNode(dragNode) ||
        isAccessDenyNode(node)
      ) {
        message.error(t('sdo_allow_privileged_drag_drop'))
        return
      }
      // 2 入组 drop 节点为组类型 && dropToGap 为 true
      if (
        isConnectionGroupNode(node) &&
        (!dropToGap || (node.expanded && node.dragOverGapBottom))
      ) {
        const connectionId = dragNode.connectionId
        const oldGroupId = dragNode.parentGroupId
        const newGroupId = node.groupId
        if (oldGroupId === newGroupId) return
        await moveConnectionToSdtGroup(connectionId, oldGroupId, newGroupId)
        dispatch(refreshOnRoot())
        return
      }

      //  3 拖拽到最外层组最上方,只有一个组的情况下
      if (isConnectionGroupNode(node) && dropPosition === -1) {
        const connectionId = dragNode.connectionId
        const oldGroupId = dragNode.parentGroupId
        const newGroupId = node.parentGroupId
        if (oldGroupId === newGroupId) return
        await moveConnectionToSdtGroup(connectionId, oldGroupId, newGroupId)
        dispatch(refreshOnRoot())
        return
      }

      // 3 排序 drop 节点为数据源类型 && drag 和 drop 节点为同组节点
      if (isConnecttionNode(node) && isSameGroupMember(node, dragNode)) {
        console.log('开始排序啦')
        const groupId = dragNode.parentGroupId
        const connectionId = dragNode.connectionId
        const preConnectionId = node.connectionId
        await sortConnection_api({ groupId, connectionId, preConnectionId })
        dispatch(refreshOnRoot())
        return
      }

      // 4 入组 drop 节点为数据源类型 && drag 和 drop 节点非同组节点
      if (isConnecttionNode(node) && !isSameGroupMember(node, dragNode)) {
        console.log('开始入组')
        const connectionId = dragNode.connectionId
        const oldGroupId = dragNode.parentGroupId
        const newGroupId = node.parentGroupId
        await moveConnectionToSdtGroup(connectionId, oldGroupId, newGroupId)
        dispatch(refreshOnRoot())
        return
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  const selectedNodeKeys = useMemo(() => {
    let newSelectedNodeKeys: string[] | undefined;
    if (mulSelectedNodes?.length > 1) {
      newSelectedNodeKeys = mulSelectedNodes?.map((item: any) => item?.key);
    }
    else if (selectedNode?.key) {
      newSelectedNodeKeys = [selectedNode.key];
    }
    else {
      newSelectedNodeKeys = [];
    }
    return newSelectedNodeKeys;
  }, [selectedNode.key, mulSelectedNodes])

  return (
    <section className={styles.sdtWrapper}>
      {SdtHeader}
      <div className={styles.sdtContentWrap}>
        <BdNavigation
          showBdNavigation={location?.state?.showBdNavigation || true}
        />
        <div className={styles.sdtContent}>
          <div className={styles.sdtSearch}>
            {searchActive ? (
              <Input
                ref={searchInputRef}
                size="small"
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                className={styles.searchBar}
                allowClear
                placeholder={t('sdo_ele_name')}
              ></Input>
            )
              :
              <Select
                allowClear
                showSearch
                size='small'
                filterOption={false}
                loading={dictLoading}
                showArrow={false}
                value={dictSearchValue}
                dropdownMatchSelectWidth={false}
                onSelect={onSelectFilterOption}
                onClear={() => { console.log('111');setDictSearchValue(null); setKeyword('') }}
                onSearch={(text) => {
                  if(!text) {
                    setDictSearchValue(null);
                  }else {
                    setDictSearchValue(text);
                  }
                  setDictOptions([])
                  onDictSearchSdtNodes({ query: text })
                }}
                className={styles.searchBar}
                placeholder={t('sdo_ele_name_data_dict')}
                >
                  {
                    dictOptions?.map((item : any) => (
                     <Select.Option key={item?.value} value={item?.value} {...item}>
                      {renderTitle(item, dictSearchValue)}
                     </Select.Option>
                    ))
                  }
                </Select>
            }
            <SdtHeaderIcon
              onClick={toggleSearchActive}
              iconType="icon-search"
              title={searchActive ? t('sdo_data_dictionary_search') : t('sdo_front_end_search')}
            />
          </div>
          <Spin spinning={treeLoading}>
            <DropdownMenu>
              <DirectoryTree
                className={styles.tree}
                height={treeHeight}
                loadData={onLoadData}
                autoExpandParent={autoExpandParent}
                multiple={true} // multiple模式支持 ctrl(Windows) / command(Mac) 复选
                treeData={filteredTreeData}
                expandAction="doubleClick"
                selectedKeys={newSdtSelectedKeysVal ? [newSdtSelectedKeysVal] : selectedNodeKeys}
                onSelect={handleNodeSelect}
                titleRender={titleRender as any}
                blockNode
                showIcon={false}
                loadedKeys={loadedKeys}
                onLoad={(loadedKeys) => dispatch(setLoadedKeys(loadedKeys))}
                expandedKeys={[...expandedKeys]}
                onExpand={(expandedKeys) => {
                  setAutoExpandParent(false);
                  dispatch(setExpandedKeys(expandedKeys))
                }
                }
                onRightClick={({ node }: { node: any }) => {
                  //同构复制开启禁止右键功能
                  if (hdrVisible) return;

                  if (noTablePermission(node)) {
                    dispatch(setNoAccessRightClick(true))
                    return
                  }
                  dispatch(setNoAccessRightClick(false))
                  const key = node.key
                  if (mulSelectedNodes.length > 1) {
                    const selectedKeys = mulSelectedNodes.map((item: any) => item?.key);
                    // 如果右键所在的node包含在已经选中的节点，则不setRightClickedNode
                    if (selectedKeys?.includes(key)) {
                      return
                    }
                  }
                  dispatch(setMulSelectedNodes([]))
                  dispatch(setRightClickedNode(node))            
                  // @ts-ignore
                  if (['table', 'collection','database', 'schema', 'oracleUser']?.includes(node.nodeType)) {
                    let root = findTreeRootNode(filteredTreeData, node);
                    let connectionName = root?.nodeName!;
                    //组内 connection 不是root 根节点
                    if (node.nodePath.includes('/root/g-')) {
                      const regex = /\/root\/(g-\d+\/)+/g;
                      const str = node.nodePath.match(regex)?.[0];

                      const parGroupPath = str && str.substring(0, str.length - 1);
                      const list = treeNodeChildrenMap?.[parGroupPath] || [];
                      const res = list?.find(item => item.connectionId === node.connectionId);
                      connectionName = res?.nodeName!;
                    }

                    const parentNode = findTreeParentNode(root, node)
                    if (!parentNode) return
                    const { nodePath, nodePathWithType } = parentNode || {}
                    dispatch(setTargetTableMessage({
                      connectionName,
                      connectionId: node?.connectionId,
                      dataSourceType: node?.connectionType,
                      nodePath, 
                      nodePathWithType,
                      // @ts-ignore
                      tableName: node?.nodeType === 'table' ? node?.nodeName : null,
                    }))
                    if (['table', 'collection']?.includes(node.nodeType)) {
                      dispatch(setTargetTableList(treeNodeChildrenMap[nodePath]))
                    }
                  }

                  // 右键表组节点直接获取节点下所有表信息
                  // @ts-ignore
                  if (node.nodeType === 'tableGroup') {
                    const root = filteredTreeData.find(({ nodePath }: { nodePath: any }) => {
                      //@ts-ignore
                      const targetArr = node.nodePath.split('/')
                      const sourceArr = nodePath.split('/')
                      return sourceArr.every((s: any, index: number) => {
                        return sourceArr[index] === targetArr[index]
                      })
                    })
                    const connectionName = root?.nodeName!
                    // @ts-ignore
                    const { nodeType, connectionId, connectionType, nodeName, nodePath, nodePathWithType, groupId } = node
                    dispatch(setTargetTableMessage({
                      connectionName,
                      connectionId,
                      dataSourceType: connectionType,
                      nodePath,
                      nodePathWithType,
                      tableName: nodeName
                    }))
                    dispatch(setTargetTableList(treeNodeChildrenMap[nodePath]))
                  }
                }}
                onDoubleClick={handleDoubleClick}
                onDrop={onDrop}
                draggable
              />
              {!treeLoading && !filteredTreeData.length && (
                <div className={styles.treePlaceholder}>{t('sdo_no_ele')}</div>
              )}
            </DropdownMenu>
          </Spin>
        </div>
      </div>
      <CreateConnectionModal onFinish={() => {
        if (!isRefreshingNode) {
          dispatch(refreshOnRoot())
        }
      }} />
      <CreateDatabaseModal />
      <AddSdtGroupModal />
      <MoveToSdtGroupModal />
      <RenameSdtNodeModal />
      <AddSdtNodeExportModal sdtNodeExportParams={sdtNodeExportParams} selectedNode={selectedNode} />
      <ModalAddSchema />
      <AddSubSdtGroupModal />
      <UpdateConnAliasModal />
      {BatchCreateConnectionModalVisible && <BatchCreateConnectionModal />}
      <DumpExportModal />
      <DumpImportModal />
      <SQLImportModal />
      <CreateFunctionModal selectedNode={selectedNode} />
      <CreateProcedureModal selectedNode={selectedNode} />
      {addGroupConnectionModalVisible && <AddGroupConnectionModal/>}
    </section>
  )
})